<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationProfileModel
 *
 * Model for the appx_application_profile table
 *
 * DATABASE TABLE STRUCTURE:
 * ========================
 *
 * Table: appx_application_profile
 * Purpose: Stores comprehensive applicant profiles with detailed information for recruitment evaluation
 *
 * COLUMNS:
 * --------
 * id                   - Primary key, auto-increment
 * applicant_id         - Foreign key to applicants table
 * exercise_id          - Foreign key to exercises table
 * full_name            - Complete name of the applicant
 * sex                  - Gender of the applicant (Male/Female)
 * bdate_age            - Birth date and calculated age information
 * place_origin         - Place of origin/birth of the applicant
 * contact_details      - Contact information (phone, email, etc.)
 * id_document_numbers  - Identity document numbers (NID, passport, etc.)
 * current_employer     - Current employer name
 * current_position     - Current job position/title
 * address_location     - Current residential address
 * qualification_text   - Education qualifications from highest to lowest, including transcripts and units
 * other_trainings      - Training and workshops from most recent to oldest
 * knowledge            - Knowledge capabilities in all areas mentioned in personal profile
 * skills_competencies  - Detailed skills and competencies of the applicant
 * job_experiences      - Job experiences from latest to oldest, classified as public/non-public service with years count
 * publications         - All publications by the applicant
 * awards               - Awards like employee of the month and other workplace/citizen awards
 * referees             - Reference contacts and information
 * other_information    - Other relevant information not captured in above fields
 * comments             - Additional comments about the applicant
 * remarks              - Official remarks and notes
 * created_by           - User ID who created the record
 * updated_by           - User ID who last updated the record
 * created_at           - Record creation timestamp
 * updated_at           - Last update timestamp
 * deleted_at           - Soft delete timestamp
 * deleted_by           - User ID who deleted the record
 */
class AppxApplicationProfileModel extends Model
{
    protected $table         = 'appx_application_profile';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'applicant_id',
        'exercise_id',
        'full_name',
        'sex',
        'bdate_age',
        'place_origin',
        'contact_details',
        'id_document_numbers',
        'current_employer',
        'current_position',
        'address_location',
        'qualification_text',
        'other_trainings',
        'knowledge',
        'skills_competencies',
        'job_experiences',
        'publications',
        'awards',
        'referees',
        'other_information',
        'comments',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'applicant_id'        => 'required|numeric',
        'exercise_id'         => 'required|numeric',
        'full_name'           => 'required|max_length[255]',
        'sex'                 => 'required|max_length[100]',
        'bdate_age'           => 'required|max_length[200]',
        'place_origin'        => 'required|max_length[255]',
        'address_location'    => 'required|max_length[255]',
        'qualification_text'  => 'required',
        'knowledge'           => 'required',
        'skills_competencies' => 'required',
        'job_experiences'     => 'required',
        'other_information'   => 'required',
        'remarks'             => 'required'
    ];

    protected $validationMessages = [
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'full_name' => [
            'required'    => 'Full name is required',
            'max_length'  => 'Full name cannot exceed 255 characters'
        ],
        'sex' => [
            'required'    => 'Sex is required',
            'max_length'  => 'Sex cannot exceed 100 characters'
        ],
        'bdate_age' => [
            'required'    => 'Birth date and age is required',
            'max_length'  => 'Birth date and age cannot exceed 200 characters'
        ],
        'place_origin' => [
            'required'    => 'Place of origin is required',
            'max_length'  => 'Place of origin cannot exceed 255 characters'
        ],
        'address_location' => [
            'required'    => 'Address location is required',
            'max_length'  => 'Address location cannot exceed 255 characters'
        ],
        'qualification_text' => [
            'required' => 'Qualification text is required'
        ],
        'knowledge' => [
            'required' => 'Knowledge is required'
        ],
        'skills_competencies' => [
            'required' => 'Skills and competencies are required'
        ],
        'job_experiences' => [
            'required' => 'Job experiences are required'
        ],
        'other_information' => [
            'required' => 'Other information is required'
        ],
        'remarks' => [
            'required' => 'Remarks are required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;



    /**
     * Get profile by applicant ID
     *
     * @param int $applicantId
     * @return array|null
     */
    public function getProfileByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->first();
    }

    /**
     * Get profiles by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getProfilesByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)->findAll();
    }



    /**
     * Search profiles by full name
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesByName($search)
    {
        return $this->like('full_name', $search)->findAll();
    }

    /**
     * Search profiles by birth date/age
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesByAge($search)
    {
        return $this->like('bdate_age', $search)->findAll();
    }

    /**
     * Get profiles by sex
     *
     * @param string $sex
     * @return array
     */
    public function getProfilesBySex($sex)
    {
        return $this->where('sex', $sex)->findAll();
    }

    /**
     * Get profiles with publications
     *
     * @return array
     */
    public function getProfilesWithPublications()
    {
        return $this->where('publications IS NOT NULL')
                    ->where('publications !=', '')
                    ->findAll();
    }

    /**
     * Get profiles with awards
     *
     * @return array
     */
    public function getProfilesWithAwards()
    {
        return $this->where('awards IS NOT NULL')
                    ->where('awards !=', '')
                    ->findAll();
    }

    /**
     * Get profiles with other trainings
     *
     * @return array
     */
    public function getProfilesWithTrainings()
    {
        return $this->where('other_trainings IS NOT NULL')
                    ->where('other_trainings !=', '')
                    ->findAll();
    }

    /**
     * Search profiles by qualification
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesByQualification($search)
    {
        return $this->like('qualification_text', $search)->findAll();
    }

    /**
     * Search profiles by skills
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesBySkills($search)
    {
        return $this->like('skills_competencies', $search)->findAll();
    }

    /**
     * Search profiles by job experience
     *
     * @param string $search
     * @return array
     */
    public function searchProfilesByExperience($search)
    {
        return $this->like('job_experiences', $search)->findAll();
    }

    /**
     * Update profile remarks
     *
     * @param int $id
     * @param string $remarks
     * @param int $updatedBy
     * @return bool
     */
    public function updateRemarks($id, $remarks, $updatedBy = null)
    {
        $data = ['remarks' => $remarks];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Update other information
     *
     * @param int $id
     * @param string $otherInfo
     * @param int $updatedBy
     * @return bool
     */
    public function updateOtherInformation($id, $otherInfo, $updatedBy = null)
    {
        $data = ['other_information' => $otherInfo];
        if ($updatedBy !== null) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Get profile statistics
     *
     * @return array
     */
    public function getProfileStatistics()
    {
        $stats = [];

        // Total profiles
        $stats['total'] = $this->countAllResults(false);

        // Profiles by sex
        $sexStats = $this->select('sex, COUNT(*) as count')
                         ->groupBy('sex')
                         ->findAll();

        $stats['by_sex'] = [];
        foreach ($sexStats as $stat) {
            $stats['by_sex'][$stat['sex']] = $stat['count'];
        }

        // Profiles with publications
        $stats['with_publications'] = $this->where('publications IS NOT NULL')
                                           ->where('publications !=', '')
                                           ->countAllResults(false);

        // Profiles with awards
        $stats['with_awards'] = $this->where('awards IS NOT NULL')
                                     ->where('awards !=', '')
                                     ->countAllResults(false);

        // Profiles with trainings
        $stats['with_trainings'] = $this->where('other_trainings IS NOT NULL')
                                        ->where('other_trainings !=', '')
                                        ->countAllResults(false);

        return $stats;
    }

    /**
     * Get complete profile with related data
     *
     * @param int $id
     * @return array|null
     */
    public function getCompleteProfile($id)
    {
        return $this->select('
                appx_application_profile.*,
                exercises.exercise_name
            ')
            ->join('exercises', 'appx_application_profile.exercise_id = exercises.id', 'left')
            ->where('appx_application_profile.id', $id)
            ->first();
    }

    /**
     * Get profiles for exercise with related data
     *
     * @param int $exerciseId
     * @return array
     */
    public function getProfilesForExercise($exerciseId)
    {
        return $this->select('
                appx_application_profile.*,
                exercises.exercise_name
            ')
            ->join('exercises', 'appx_application_profile.exercise_id = exercises.id', 'left')
            ->where('appx_application_profile.exercise_id', $exerciseId)
            ->orderBy('appx_application_profile.created_at', 'DESC')
            ->findAll();
    }

    /**
     * Get profiled applicants grouped by exercise
     *
     * @param int $exerciseId
     * @return array
     */
    public function getProfiledApplicantsByExercise($exerciseId)
    {
        return $this->select('
                applicant_id,
                full_name,
                sex as gender,
                contact_details,
                COUNT(id) as profile_count
            ')
            ->where('exercise_id', $exerciseId)
            ->groupBy('applicant_id')
            ->orderBy('full_name', 'ASC')
            ->findAll();
    }

    /**
     * Get profiled applicant details by applicant and exercise
     *
     * @param int $applicantId
     * @param int $exerciseId
     * @return array|null
     */
    public function getProfiledApplicantDetails($applicantId, $exerciseId)
    {
        return $this->where('applicant_id', $applicantId)
                    ->where('exercise_id', $exerciseId)
                    ->first();
    }
}

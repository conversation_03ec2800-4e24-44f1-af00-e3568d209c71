<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3">Job Openings</h1>
            <p class="text-muted">Browse through available job exercises and click to view positions.</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?= base_url('applicant/jobs') ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="org_id" class="form-label">Filter by Organization</label>
                    <select name="org_id" id="org_id" class="form-select">
                        <option value="">All Organizations</option>
                        <?php foreach ($organizations as $org): ?>
                            <option value="<?= $org['id'] ?>" <?= $selectedOrgId == $org['id'] ? 'selected' : '' ?>>
                                <?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="position_group" class="form-label">Filter by Position Group</label>
                    <select name="position_group" id="position_group" class="form-select">
                        <option value="">All Position Groups</option>
                        <?php foreach ($positionGroups as $group): ?>
                            <option value="<?= $group['id'] ?>" <?= $selectedPositionGroup == $group['id'] ? 'selected' : '' ?>>
                                <?= esc($group['group_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="advertisement_type" class="form-label">Advertisement Type</label>
                    <select name="advertisement_type" id="advertisement_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="0" <?= $selectedAdvertisementType === '0' ? 'selected' : '' ?>>External</option>
                        <option value="1" <?= $selectedAdvertisementType === '1' ? 'selected' : '' ?>>Internal</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="Search by position, location, organization, position groups..."
                           value="<?= esc($searchTerm ?? '') ?>">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>

            <!-- Filter Results Summary -->
            <?php if ($selectedOrgId || $searchTerm): ?>
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted">
                                Showing <?= $filteredJobs ?> of <?= $totalJobs ?> job exercises
                                <?php if ($selectedOrgId): ?>
                                    <?php
                                    $selectedOrgName = '';
                                    foreach ($organizations as $org) {
                                        if ($org['id'] == $selectedOrgId) {
                                            $selectedOrgName = $org['org_name'];
                                            break;
                                        }
                                    }
                                    ?>
                                    • Filtered by: <strong><?= esc($selectedOrgName) ?></strong>
                                <?php endif; ?>
                                <?php if ($searchTerm): ?>
                                    • Search: <strong>"<?= esc($searchTerm) ?>"</strong>
                                <?php endif; ?>
                            </span>
                        </div>
                        <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="mt-3 pt-3 border-top">
                    <span class="text-muted">Showing all <?= $totalJobs ?> available positions</span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- View Toggle Buttons -->
    <?php if (!empty($jobData)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">Available Job Positions</h5>
            <div class="btn-group" role="group" aria-label="View toggle">
                <button type="button" class="btn btn-outline-secondary active" id="gridViewBtn" onclick="toggleView('grid')">
                    <i class="fas fa-th me-2"></i>Grid
                </button>
                <button type="button" class="btn btn-outline-secondary" id="tableViewBtn" onclick="toggleView('table')">
                    <i class="fas fa-list me-2"></i>Table
                </button>
            </div>
        </div>
    <?php endif; ?>

    <!-- File Upload Requirement Notice -->
    <?php if (!empty($jobData) && !($hasUploadedFiles ?? true)): ?>
        <div class="alert alert-warning mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">Upload Required Documents First</h6>
                    <p class="mb-2">Before you can apply for any position, you must upload at least one supporting document (CV, certificates, etc.).</p>
                    <a href="<?= base_url('applicant/profile/files/create') ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-upload me-1"></i>Upload Documents Now
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (empty($jobData)): ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                <h5>No Job Positions Available</h5>
                <p class="text-muted">There are currently no published job positions.</p>
                <?php if (isset($error)): ?>
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i><?= esc($error) ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- Grid View -->
        <div class="grid-view active" id="gridView">
            <div class="row">
                <?php foreach ($jobData as $data): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <?= esc($data['organization']['org_name']) ?>
                                </h5>
                                <?php if (!empty($data['organization']['logo_path'])): ?>
                                    <div class="text-center">
                                        <img src="<?= base_url(str_replace('public/', '', $data['organization']['logo_path'])) ?>"
                                             alt="Organization Logo"
                                             class="img-fluid"
                                             style="max-height: 50px;">
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-title text-primary">
                                        <?= esc($data['position']['designation']) ?>
                                    </h6>
                                    <div class="d-flex flex-column gap-1">
                                        <?php if (isset($data['exercise']['is_internal'])): ?>
                                            <?php if ($data['exercise']['is_internal'] == 1): ?>
                                                <span class="badge bg-warning text-dark">Internal</span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">External</span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if ($data['has_applied']): ?>
                                            <span class="badge bg-success">Applied</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <p class="card-text small">
                                    <strong>Position Ref:</strong> <?= esc($data['position']['position_reference']) ?><br>
                                    <strong>Position Group:</strong> <?= esc($data['position']['position_group_name'] ?? 'N/A') ?><br>
                                    <strong>Classification:</strong> <?= esc($data['position']['classification']) ?><br>
                                    <strong>Award:</strong> <?= esc($data['position']['award'] ?? 'N/A') ?><br>
                                    <strong>Location:</strong> <?= esc($data['position']['location'] ?? 'N/A') ?><br>
                                    <strong>Salary Grade/Class:</strong> <?= esc($data['position']['salary_grade'] ?? 'N/A') ?><br>
                                    <strong>Annual Salary Range:</strong> <?= esc($data['position']['annual_salary_range'] ?? 'N/A') ?><br>
                                    <strong>Closes:</strong> <?= date('M d, Y', strtotime($data['exercise']['publish_date_to'])) ?>
                                </p>
                                <div class="text-end">
                                    <?php if ($data['has_applied']): ?>
                                        <a href="<?= base_url('applicant/application/' . $data['position']['id']) ?>"
                                           class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-check me-1"></i>View Application
                                        </a>
                                    <?php else: ?>
                                        <a href="<?= base_url('applicant/jobs/position/' . $data['position']['id']) ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Details and Apply
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Table View -->
        <div class="table-view" id="tableView" style="display: none;">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Organization</th>
                                    <th>Position</th>
                                    <th>Position Group</th>
                                    <th>Award</th>
                                    <th>Location</th>
                                    <th>Salary Grade</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Closes</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($jobData as $data): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($data['organization']['logo_path'])): ?>
                                                    <img src="<?= base_url(str_replace('public/', '', $data['organization']['logo_path'])) ?>"
                                                         alt="Logo" class="me-2" style="width: 30px; height: 30px; object-fit: contain;">
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?= esc($data['organization']['org_name']) ?></strong><br>
                                                    <small class="text-muted"><?= esc($data['organization']['org_code']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?= esc($data['position']['designation']) ?></strong><br>
                                            <small class="text-muted">Ref: <?= esc($data['position']['position_reference']) ?></small>
                                        </td>
                                        <td><?= esc($data['position']['position_group_name'] ?? 'N/A') ?></td>
                                        <td><?= esc($data['position']['award'] ?? 'N/A') ?></td>
                                        <td><?= esc($data['position']['location'] ?? 'N/A') ?></td>
                                        <td>
                                            <strong><?= esc($data['position']['salary_grade'] ?? 'N/A') ?></strong><br>
                                            <small class="text-muted"><?= esc($data['position']['annual_salary_range'] ?? 'N/A') ?></small>
                                        </td>
                                        <td>
                                            <?php if (isset($data['exercise']['is_internal'])): ?>
                                                <?php if ($data['exercise']['is_internal'] == 1): ?>
                                                    <span class="badge bg-warning text-dark">Internal</span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">External</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($data['has_applied']): ?>
                                                <span class="badge bg-success">Applied</span>
                                            <?php else: ?>
                                                <span class="badge bg-info">Available</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('M d, Y \a\t g:i A', strtotime($data['exercise']['publish_date_to'])) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if ($data['has_applied']): ?>
                                                    <a href="<?= base_url('applicant/application/' . $data['position']['id']) ?>"
                                                       class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-check me-1"></i>View App
                                                    </a>
                                                <?php else: ?>
                                                    <a href="<?= base_url('applicant/jobs/position/' . $data['position']['id']) ?>"
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye me-1"></i>View Details and Apply
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?= $this->section('scripts') ?>
<script>
// View toggle function
function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    const gridBtn = document.getElementById('gridViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'grid') {
        gridView.style.display = 'block';
        tableView.style.display = 'none';
        gridBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        gridView.style.display = 'none';
        tableView.style.display = 'block';
        gridBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}

$(document).ready(function() {
    // Initialize Select2 for dropdown filters
    $('#org_id').select2({
        theme: 'bootstrap-5',
        placeholder: 'All Organizations',
        allowClear: true,
        width: '100%'
    });

    $('#position_group').select2({
        theme: 'bootstrap-5',
        placeholder: 'All Position Groups',
        allowClear: true,
        width: '100%'
    });

    // Auto-submit form when any filter changes
    $('#org_id, #position_group, #advertisement_type').on('change', function() {
        $(this).closest('form').submit();
    });

    // Add search functionality with Enter key
    $('#search').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            $(this).closest('form').submit();
        }
    });

    // Add loading state to filter button
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Filtering...');
        submitBtn.prop('disabled', true);

        // Re-enable after a short delay (in case of quick response)
        setTimeout(function() {
            submitBtn.html(originalText);
            submitBtn.prop('disabled', false);
        }, 2000);
    });



    // Add animation to cards
    $('.card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('animate__animated animate__fadeInUp');
    });
});
</script>

<style>
/* Add some custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 40px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.animate__animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.animate__fadeInUp {
    animation-name: fadeInUp;
}

/* Highlight search results */
mark {
    background-color: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
}

/* Hover effects for cards */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Filter section styling */
.card-body .border-top {
    border-color: #dee2e6 !important;
}

/* View toggle buttons */
.btn-group .btn.active {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-group .btn:not(.active) {
    background-color: white;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-group .btn:not(.active):hover {
    background-color: #f8f9fa;
    border-color: #dc3545;
    color: #dc3545;
}

/* Table view styling */
.table-view .table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table-view .table td {
    vertical-align: middle;
}

.table-view .table tbody tr:hover {
    background-color: #f8f9fa;
}
</style>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
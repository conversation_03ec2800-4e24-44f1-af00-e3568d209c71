<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\ResponseInterface;

class PrescreeningAIController extends Controller
{
    /**
     * Main prescreening analysis endpoint for multiple PDFs
     * Analyzes PDFs directly against prescreening criteria
     */
    public function analyzePdfsForPrescreening()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $pdfs = $input['pdfs'] ?? [];
            $criteria = $input['criteria'] ?? [];

            if (empty($pdfs)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'PDFs are required for analysis'
                ])->setStatusCode(400);
            }

            if (empty($criteria)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Prescreening criteria are required'
                ])->setStatusCode(400);
            }

            // Call helper function for AI analysis
            $analysisResult = gemini_prescreening_pdfs_analysis($pdfs, $criteria);

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $analysisResult['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to analyze PDFs for prescreening'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Prescreening PDFs AI analysis error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during PDF analysis. Please try again.'
            ])->setStatusCode(500);
        }
    }

    /**
     * Legacy prescreening analysis endpoint
     * Analyzes images against prescreening criteria
     */
    public function analyzeForPrescreening()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $images = $input['images'] ?? [];
            $criteria = $input['criteria'] ?? [];

            if (empty($images)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Images are required for analysis'
                ])->setStatusCode(400);
            }

            if (empty($criteria)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Prescreening criteria are required'
                ])->setStatusCode(400);
            }

            // Call helper function for AI analysis
            $analysisResult = gemini_prescreening_analysis($images, $criteria);

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $analysisResult['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to analyze for prescreening'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Prescreening AI analysis error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during analysis. Please try again.'
            ])->setStatusCode(500);
        }
    }

    /**
     * PDF multimodal analysis endpoint
     * Analyzes PDF documents using multimodal capabilities
     */
    public function analyzePdfMultimodal()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $pdfBase64 = $input['pdf_base64'] ?? '';
            $fileName = $input['file_name'] ?? 'document.pdf';

            if (empty($pdfBase64)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'PDF data is required for analysis'
                ])->setStatusCode(400);
            }

            // Call helper function for PDF analysis
            $analysisResult = gemini_pdf_multimodal_analysis($pdfBase64, $fileName);

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $analysisResult['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to analyze PDF'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'PDF multimodal analysis error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during PDF analysis. Please try again.'
            ])->setStatusCode(500);
        }
    }

    /**
     * File profile analysis endpoint
     * Creates detailed file profiles from images
     */
    public function analyzeFileProfile()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $images = $input['images'] ?? [];
            $fileName = $input['file_name'] ?? 'document';
            $chunkNumber = $input['chunk_number'] ?? 1;
            $totalChunks = $input['total_chunks'] ?? 1;
            $totalPages = $input['total_pages'] ?? 0;
            $startPage = $input['start_page'] ?? 1;
            $endPage = $input['end_page'] ?? 0;

            if (empty($images)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Images are required for file profiling'
                ])->setStatusCode(400);
            }

            // Call helper function for file profile analysis
            $analysisResult = gemini_file_profile_analysis(
                $images, 
                $fileName, 
                $chunkNumber, 
                $totalChunks, 
                $totalPages, 
                $startPage, 
                $endPage
            );

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $analysisResult['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to analyze file profile'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'File profile analysis error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during file profile analysis. Please try again.'
            ])->setStatusCode(500);
        }
    }

    /**
     * Chunked analysis endpoint
     * Handles analysis of large documents in chunks
     */
    public function analyzeInChunks()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $chunks = $input['chunks'] ?? [];
            $fileName = $input['file_name'] ?? 'document';

            if (empty($chunks)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Chunks are required for analysis'
                ])->setStatusCode(400);
            }

            // Call helper function for chunked analysis
            $analysisResult = gemini_chunked_analysis($chunks, $fileName);

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $analysisResult['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to analyze chunks'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Chunked analysis error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during chunked analysis. Please try again.'
            ])->setStatusCode(500);
        }
    }

    /**
     * Generate comprehensive applicant profiling using AI
     * Analyzes all applicant data and files to populate profiling form
     */
    public function generateApplicantProfiling()
    {
        try {
            // Only allow POST requests
            if ($this->request->getMethod() !== 'POST') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only POST requests allowed'
                ])->setStatusCode(405);
            }

            // Get the input data
            $input = $this->request->getJSON(true);
            $applicantData = $input['applicant_data'] ?? [];
            $applicantFiles = $input['applicant_files'] ?? [];
            $processedFiles = $input['processed_files'] ?? [];
            $exerciseClosingDate = $input['exercise_closing_date'] ?? null;

            if (empty($applicantData)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Applicant data is required for profiling'
                ])->setStatusCode(400);
            }

            // Call helper function for comprehensive applicant profiling
            $analysisResult = gemini_comprehensive_applicant_profiling($applicantData, $applicantFiles, $processedFiles, $exerciseClosingDate);

            if (isset($analysisResult['success']) && $analysisResult['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'profiling' => $analysisResult['profiling']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $analysisResult['message'] ?? 'Failed to generate applicant profiling'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Applicant profiling generation error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sorry, I encountered an error during profiling generation. Please try again.'
            ])->setStatusCode(500);
        }
    }
}

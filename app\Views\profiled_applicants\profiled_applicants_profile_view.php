<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('dashboard') ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profiled_applicants') ?>">Profiled Applicants</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profiled_applicants/exercise/' . $exercise['id'] . '/applicants') ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($profile['full_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Profiled Applicant Details</h2>
                    <p class="text-muted mb-0"><?= esc($profile['full_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('profiled_applicants/exercise/' . $exercise['id'] . '/applicants') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Applicants
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="row">
        <!-- Personal Information -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Full Name:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['full_name']) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Gender:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge bg-<?= $profile['sex'] === 'Male' ? 'primary' : 'info' ?>">
                                <?= esc($profile['sex']) ?>
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Birth Date & Age:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['bdate_age']) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Place of Origin:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['place_origin']) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Contact Details:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['contact_details']) ?></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Address:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['address_location']) ?></div>
                    </div>
                    <div class="row mb-0">
                        <div class="col-sm-4"><strong>ID Numbers:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['id_document_numbers']) ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employment Information -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>Employment Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Current Employer:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['current_employer']) ?></div>
                    </div>
                    <div class="row mb-0">
                        <div class="col-sm-4"><strong>Current Position:</strong></div>
                        <div class="col-sm-8"><?= esc($profile['current_position']) ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Qualifications and Experience -->
    <div class="row">
        <!-- Qualifications -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>Qualifications
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <?= nl2br(esc($profile['qualification_text'])) ?>
                    </div>
                    <?php if (!empty($profile['other_trainings'])): ?>
                        <h6 class="text-info">Other Trainings:</h6>
                        <div class="mb-0">
                            <?= nl2br(esc($profile['other_trainings'])) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Skills and Experience -->
    <div class="row">
        <!-- Knowledge -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-brain me-2"></i>Knowledge
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['knowledge'])) ?>
                </div>
            </div>
        </div>

        <!-- Skills & Competencies -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>Skills & Competencies
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['skills_competencies'])) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Experience -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Job Experience
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['job_experiences'])) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="row">
        <!-- Publications -->
        <?php if (!empty($profile['publications'])): ?>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book me-2"></i>Publications
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['publications'])) ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Awards -->
        <?php if (!empty($profile['awards'])): ?>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>Awards
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['awards'])) ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Referees and Other Information -->
    <div class="row">
        <!-- Referees -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>Referees
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['referees'])) ?>
                </div>
            </div>
        </div>

        <!-- Other Information -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Other Information
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['other_information'])) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Comments and Remarks -->
    <div class="row">
        <!-- Comments -->
        <?php if (!empty($profile['comments'])): ?>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2"></i>Comments
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['comments'])) ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Remarks -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sticky-note me-2"></i>Remarks
                    </h5>
                </div>
                <div class="card-body">
                    <?= nl2br(esc($profile['remarks'])) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Metadata -->
    <div class="row">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>Profile Information
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Profile Created:</strong> <?= date('M d, Y H:i', strtotime($profile['created_at'])) ?></p>
                            <p class="mb-0"><strong>Created By:</strong> User ID <?= esc($profile['created_by']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <?php if (!empty($profile['updated_at'])): ?>
                                <p class="mb-1"><strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($profile['updated_at'])) ?></p>
                                <p class="mb-0"><strong>Updated By:</strong> User ID <?= esc($profile['updated_by']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Profiling Applicants</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('profile_applications_exercise') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Exercises
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Exercise Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-body">
                <h5 class="card-title text-red">Exercise Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                        <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Status:</strong> 
                            <span class="badge bg-<?= $exercise['status'] === 'selection' ? 'success' : 'warning' ?>">
                                <?= ucfirst(esc($exercise['status'])) ?>
                            </span>
                        </p>
                        <p><strong>Total Applicants for Profiling:</strong> 
                            <span class="badge bg-primary"><?= count($applicants) ?></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Applicants List -->
<div class="row">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> Applicants Available for Profiling
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($applicants)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No applicants found for profiling in this exercise. 
                        Applicants must have passed pre-screening to be available for profiling.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table id="applicantsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="20%">Full Name</th>
                                    <th width="10%">Gender</th>
                                    <th width="20%">Email</th>
                                    <th width="15%">Contact</th>
                                    <th width="8%">Applications</th>
                                    <th width="12%">Profiling Status</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $count = 1; ?>
                                <?php foreach ($applicants as $applicant): ?>
                                    <tr>
                                        <td><?= $count++ ?></td>
                                        <td>
                                            <strong><?= esc($applicant['full_name']) ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $applicant['gender'] === 'Male' ? 'primary' : 'info' ?>">
                                                <?= esc($applicant['gender']) ?>
                                            </span>
                                        </td>
                                        <td><?= esc($applicant['email_address']) ?></td>
                                        <td><?= esc($applicant['contact_details']) ?></td>
                                        <td>
                                            <span class="badge bg-success"><?= esc($applicant['application_count']) ?></span>
                                        </td>
                                        <td>
                                            <?php if (!empty($applicant['profile_status'])): ?>
                                                <span class="badge bg-success">Profiled</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Not Profiled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('profile_applications_exercise/applicant_profile/' . $applicant['applicant_id'] . '/' . $exercise['id']) ?>"
                                               class="btn btn-sm btn-outline-primary" title="View Profiling Profile">
                                                <i class="fas fa-user-check"></i> View Profile
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicantsTable').DataTable({
        responsive: true,
        order: [[1, 'asc']], // Sort by name
        language: {
            search: "Search applicants:",
            lengthMenu: "Show _MENU_ applicants per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applicants",
            emptyTable: "No applicants available for profiling",
        }
    });
});
</script>
<?= $this->endSection() ?>

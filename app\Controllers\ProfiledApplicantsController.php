<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class ProfiledApplicants<PERSON><PERSON>roller extends Controller
{
    protected $session;
    protected $exerciseModel;
    protected $profileModel;

    public function __construct()
    {
        $this->session = \Config\Services::session();
        $this->exerciseModel = new \App\Models\ExerciseModel();
        $this->profileModel = new \App\Models\AppxApplicationProfileModel();
    }

    /**
     * Display list of exercises with profiled applicants (GET)
     * URI: /profiled_applicants
     */
    public function index()
    {
        // Get exercises with status = 'selection'
        $exercises = $this->exerciseModel->where('status', 'selection')->findAll();

        $data = [
            'title' => 'Exercises with Profiled Applicants',
            'menu' => 'profiled_applicants',
            'exercises' => $exercises
        ];

        return view('profiled_applicants/profiled_applicants_exercise_list', $data);
    }

    /**
     * Display profiled applicants for a specific exercise (GET)
     * URI: /profiled_applicants/exercise/{exerciseId}/applicants
     */
    public function exerciseApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercise details
        $exercise = $this->exerciseModel->where('id', $exerciseId)
                                      ->where('org_id', $orgId)
                                      ->first();

        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('profiled_applicants'));
        }

        // Get profiled applicants for this exercise
        $applicants = $this->profileModel->getProfiledApplicantsByExercise($exerciseId);

        $data = [
            'title' => 'Profiled Applicants in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'profiled_applicants',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('profiled_applicants/profiled_applicants_exercise_applicants', $data);
    }

    /**
     * Display profiled applicant details (GET)
     * URI: /profiled_applicants/applicant/{applicantId}/exercise/{exerciseId}
     */
    public function applicantProfile($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercise details
        $exercise = $this->exerciseModel->where('id', $exerciseId)
                                      ->where('org_id', $orgId)
                                      ->first();

        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('profiled_applicants'));
        }

        // Get profiled applicant details
        $profile = $this->profileModel->getProfiledApplicantDetails($applicantId, $exerciseId);

        if (!$profile) {
            $this->session->setFlashdata('error', 'Profiled applicant not found.');
            return redirect()->to(base_url('profiled_applicants/exercise/' . $exerciseId . '/applicants'));
        }

        $data = [
            'title' => 'Profiled Applicant: ' . esc($profile['full_name']),
            'menu' => 'profiled_applicants',
            'exercise' => $exercise,
            'profile' => $profile
        ];

        return view('profiled_applicants/profiled_applicants_profile_view', $data);
    }
}

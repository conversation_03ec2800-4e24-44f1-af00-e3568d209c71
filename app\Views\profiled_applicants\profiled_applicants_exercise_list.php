<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('dashboard') ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Profiled Applicants
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Profiled Applicants</h2>
                    <p class="text-muted mb-0">View profiled applicants by exercise</p>
                </div>
                <div>
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercises List -->
    <div class="row">
        <div class="col-12">
            <div class="card hover-card">
                <div class="card-header bg-red text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-list"></i> Exercises Available
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($exercises)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No exercises found with selection status. 
                            Only exercises in selection phase are available for viewing profiled applicants.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table id="exercisesTable" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="30%">Exercise Name</th>
                                        <th width="15%">Advertisement No.</th>
                                        <th width="15%">Status</th>
                                        <th width="15%">Created Date</th>
                                        <th width="20%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($exercises as $exercise): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <strong><?= esc($exercise['exercise_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($exercise['advertisement_no']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?= esc(ucfirst($exercise['status'])) ?></span>
                                            </td>
                                            <td>
                                                <small><?= date('M d, Y', strtotime($exercise['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('profiled_applicants/exercise/' . $exercise['id'] . '/applicants') ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Profiled Applicants">
                                                    <i class="fas fa-users"></i> View Profiled Applicants
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Information
                    </h6>
                    <p class="card-text">
                        This section shows exercises that are in the selection phase and have profiled applicants. 
                        Select an exercise to view the list of applicants who have been profiled.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#exercisesTable').DataTable({
        responsive: true,
        order: [[4, 'desc']], // Sort by created date (newest first)
        language: {
            search: "Search exercises:",
            lengthMenu: "Show _MENU_ exercises per page",
            info: "Showing _START_ to _END_ of _TOTAL_ exercises",
            emptyTable: "No exercises available",
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/exercise/' . $exercise['id'] . '/applicants') ?>">Profiling Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Profiling Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('profile_applications_exercise/exercise/' . $exercise['id'] . '/applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Two Column Layout -->
<div class="row">
    <!-- Left Column - Applicant Profile -->
    <div class="col-lg-8">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Applicant Profile: <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true" style="color: #333 !important;">
                            <i class="fas fa-user-circle"></i> Applicant Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false" style="color: #333 !important;">
                            <i class="fas fa-file-pdf"></i> Files (<?= count($applicant_files) ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="profileTabsContent">
                    <!-- Applicant Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <?php $firstApp = $applications[0]; ?>

                                <!-- 1. Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-user me-2"></i>Personal Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Basic details and contact info</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Full Name:</strong> <?= esc($firstApp['first_name'] . ' ' . $firstApp['last_name']) ?></p>
                                                <p><strong>First Name:</strong> <?= esc($firstApp['first_name']) ?></p>
                                                <p><strong>Last Name:</strong> <?= esc($firstApp['last_name']) ?></p>
                                                <p><strong>Gender:</strong> <?= esc($firstApp['gender']) ?></p>
                                                <p><strong>Date of Birth:</strong>
                                                    <span id="dobDisplay"><?= esc($firstApp['date_of_birth']) ?></span>
                                                    <span id="ageDisplay" class="badge bg-secondary ms-2"></span>
                                                </p>
                                                <p><strong>Place of Origin:</strong> <?= esc($firstApp['place_of_origin']) ?></p>
                                                <p><strong>Citizenship:</strong> <?= esc($firstApp['citizenship']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Email Address:</strong> <?= esc($firstApp['email_address']) ?></p>
                                                <p><strong>Contact Details:</strong> <?= esc($firstApp['contact_details']) ?></p>
                                                <p><strong>Location Address:</strong> <?= esc($firstApp['location_address']) ?></p>
                                                <p><strong>Marital Status:</strong> <?= esc($firstApp['marital_status']) ?></p>
                                                <?php if (!empty($firstApp['date_of_marriage'])): ?>
                                                    <p><strong>Date of Marriage:</strong> <?= esc($firstApp['date_of_marriage']) ?></p>
                                                <?php endif; ?>
                                                <?php if (!empty($firstApp['spouse_employer'])): ?>
                                                    <p><strong>Spouse Employer:</strong> <?= esc($firstApp['spouse_employer']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 2. Documents & ID -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-id-card me-2"></i>Documents & ID
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Identification and records</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>ID Numbers:</strong> <?= esc($firstApp['id_numbers']) ?></p>
                                                <p><strong>Offence Convicted:</strong> <?= esc($firstApp['offence_convicted'] ?? 'Not specified') ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <?php if (!empty($firstApp['id_photo_path'])): ?>
                                                    <p><strong>ID Photo:</strong></p>
                                                    <img src="<?= base_url($firstApp['id_photo_path']) ?>" alt="ID Photo" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                                <?php endif; ?>
                                                <?php if (!empty($firstApp['signature_path'])): ?>
                                                    <p><strong>Signature:</strong></p>
                                                    <img src="<?= base_url($firstApp['signature_path']) ?>" alt="Signature" class="img-thumbnail" style="max-width: 200px; max-height: 100px;">
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 3. Employment -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-briefcase me-2"></i>Employment
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Current work information</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Current Employer:</strong> <?= esc($firstApp['current_employer']) ?></p>
                                                <p><strong>Current Position:</strong> <?= esc($firstApp['current_position']) ?></p>
                                                <p><strong>Current Salary:</strong> <?= esc($firstApp['current_salary']) ?></p>
                                                <p><strong>Public Servant:</strong>
                                                    <span class="badge bg-<?= $firstApp['is_public_servant'] ? 'success' : 'secondary' ?>">
                                                        <?= $firstApp['is_public_servant'] ? 'Yes' : 'No' ?>
                                                    </span>
                                                </p>
                                            </div>
                                            <div class="col-md-6">
                                                <?php if ($firstApp['is_public_servant']): ?>
                                                    <p><strong>Public Service File Number:</strong> <?= esc($firstApp['public_service_file_number']) ?></p>
                                                <?php endif; ?>
                                                <?php if (!empty($firstApp['employee_of_org_id'])): ?>
                                                    <p><strong>Employee of Organization ID:</strong> <?= esc($firstApp['employee_of_org_id']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 4. Work Experience -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-history me-2"></i>Work Experience
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Previous employment history</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Work experience details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 5. Education -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-graduation-cap me-2"></i>Education
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Academic qualifications</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Education details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 6. Family Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-users me-2"></i>Family Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Family details and dependents</small>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <strong>Children:</strong>
                                                    <div id="childrenDisplay" class="mt-2">
                                                        <!-- Children will be displayed here in table format -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Referees -->
                                        <?php if (!empty($firstApp['referees'])): ?>
                                        <div class="mt-4">
                                            <strong>Referees:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= nl2br(esc($firstApp['referees'])) ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- 7. Achievements -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-star me-2"></i>Achievements
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Awards and accomplishments</small>
                                        <div class="row">
                                            <?php if (!empty($firstApp['publications'])): ?>
                                            <div class="col-md-6">
                                                <strong>Publications:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['publications'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (!empty($firstApp['awards'])): ?>
                                            <div class="col-md-6">
                                                <strong>Awards:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['awards'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (empty($firstApp['publications']) && empty($firstApp['awards'])): ?>
                                            <div class="col-12">
                                                <div class="bg-light p-3 rounded">
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        No achievements or awards information provided.
                                                    </p>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Additional Information -->
                                        <div class="mt-4">
                                            <strong>How did you hear about us:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= esc($firstApp['how_did_you_hear_about_us']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No application details found for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Files Tab -->
                    <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                        <div class="p-4">
                            <?php if (!empty($applicant_files)): ?>
                                <div class="mb-3">
                                    <h6 class="text-red">PDF Files Viewer</h6>
                                    <p class="text-muted">All PDF files are merged and displayed below. Scroll through to view all documents.</p>
                                </div>

                                <!-- PDF Viewer Container -->
                                <div id="pdfViewerContainer" style="height: 800px; border: 1px solid #ddd; border-radius: 8px; overflow-y: auto;">
                                    <div id="pdfViewer" class="p-3">
                                        <div class="text-center p-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading PDFs...</span>
                                            </div>
                                            <p class="mt-2">Loading PDF files...</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Files List -->
                                <div class="mt-4">
                                    <h6 class="text-red">Files List</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>File Title</th>
                                                    <th>Description</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($applicant_files as $file): ?>
                                                    <tr>
                                                        <td><?= esc($file['file_title']) ?></td>
                                                        <td><?= esc($file['file_description'] ?? 'Application file') ?></td>
                                                        <td>
                                                            <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye"></i> View
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No files uploaded for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column - Profiling Form -->
    <div class="col-lg-4">
        <div class="card hover-card sticky-profiling-card">
            <div class="card-header bg-red text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit"></i> Applicant Profiling
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-light" id="aiProfilingBtn" title="Generate AI-powered profiling">
                        <i class="fas fa-robot"></i> AI Profiling
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="profilingForm" action="<?= base_url('profile_applications_exercise/save_profiling_results') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="applicant_id" value="<?= $applicant_id ?>">
                    <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                    <input type="hidden" name="application_id" value="<?= $applications[0]['id'] ?? '' ?>">
                    <input type="hidden" name="position_id" value="<?= $applications[0]['position_id'] ?? '' ?>">

                    <!-- Full Name -->
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name"
                               value="<?= esc($existing_profile['full_name'] ?? ($applications[0]['first_name'] . ' ' . $applications[0]['last_name'])) ?>" required>
                    </div>

                    <!-- Sex -->
                    <div class="mb-3">
                        <label for="sex" class="form-label">Sex <span class="text-danger">*</span></label>
                        <select class="form-control" id="sex" name="sex" required>
                            <option value="">Select Gender</option>
                            <option value="Male" <?= ($existing_profile['sex'] ?? $applications[0]['gender'] ?? '') === 'Male' ? 'selected' : '' ?>>Male</option>
                            <option value="Female" <?= ($existing_profile['sex'] ?? $applications[0]['gender'] ?? '') === 'Female' ? 'selected' : '' ?>>Female</option>
                        </select>
                    </div>

                    <!-- Birth Date & Age -->
                    <div class="mb-3">
                        <label for="bdate_age" class="form-label">Birth Date & Age <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="bdate_age" name="bdate_age"
                               value="<?= esc($existing_profile['bdate_age'] ?? $applications[0]['date_of_birth'] ?? '') ?>"
                               placeholder="e.g., 1990-01-15 (33 years old)" required>
                    </div>

                    <!-- Place of Origin -->
                    <div class="mb-3">
                        <label for="place_origin" class="form-label">Place of Origin <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="place_origin" name="place_origin"
                               value="<?= esc($existing_profile['place_origin'] ?? $applications[0]['place_of_origin'] ?? '') ?>" required>
                    </div>

                    <!-- Contact Details -->
                    <div class="mb-3">
                        <label for="contact_details" class="form-label">Contact Details</label>
                        <textarea class="form-control" id="contact_details" name="contact_details" rows="2"><?= esc($existing_profile['contact_details'] ?? $applications[0]['contact_details'] ?? '') ?></textarea>
                    </div>

                    <!-- ID Document Numbers -->
                    <div class="mb-3">
                        <label for="id_document_numbers" class="form-label">ID Document Numbers</label>
                        <textarea class="form-control" id="id_document_numbers" name="id_document_numbers" rows="2"><?= esc($existing_profile['id_document_numbers'] ?? $applications[0]['id_numbers'] ?? '') ?></textarea>
                    </div>

                    <!-- Current Employer -->
                    <div class="mb-3">
                        <label for="current_employer" class="form-label">Current Employer</label>
                        <input type="text" class="form-control" id="current_employer" name="current_employer"
                               value="<?= esc($existing_profile['current_employer'] ?? $applications[0]['current_employer'] ?? '') ?>">
                    </div>

                    <!-- Current Position -->
                    <div class="mb-3">
                        <label for="current_position" class="form-label">Current Position</label>
                        <input type="text" class="form-control" id="current_position" name="current_position"
                               value="<?= esc($existing_profile['current_position'] ?? $applications[0]['current_position'] ?? '') ?>">
                    </div>

                    <!-- Address Location -->
                    <div class="mb-3">
                        <label for="address_location" class="form-label">Address Location <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address_location" name="address_location" rows="2" required><?= esc($existing_profile['address_location'] ?? $applications[0]['location_address'] ?? '') ?></textarea>
                    </div>

                    <!-- Qualification Text -->
                    <div class="mb-3">
                        <label for="qualification_text" class="form-label">Qualifications <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="qualification_text" name="qualification_text" rows="4"
                                  placeholder="Education qualifications from highest to lowest, including transcripts and units" required><?= esc($existing_profile['qualification_text'] ?? '') ?></textarea>
                    </div>

                    <!-- Other Trainings -->
                    <div class="mb-3">
                        <label for="other_trainings" class="form-label">Other Trainings</label>
                        <textarea class="form-control" id="other_trainings" name="other_trainings" rows="3"
                                  placeholder="Training and workshops from most recent to oldest"><?= esc($existing_profile['other_trainings'] ?? '') ?></textarea>
                    </div>

                    <!-- Knowledge -->
                    <div class="mb-3">
                        <label for="knowledge" class="form-label">Knowledge <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="knowledge" name="knowledge" rows="4"
                                  placeholder="Knowledge capabilities in all areas mentioned in personal profile" required><?= esc($existing_profile['knowledge'] ?? '') ?></textarea>
                    </div>

                    <!-- Skills & Competencies -->
                    <div class="mb-3">
                        <label for="skills_competencies" class="form-label">Skills & Competencies <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="skills_competencies" name="skills_competencies" rows="4"
                                  placeholder="Detailed skills and competencies of the applicant" required><?= esc($existing_profile['skills_competencies'] ?? '') ?></textarea>
                    </div>

                    <!-- Job Experiences -->
                    <div class="mb-3">
                        <label for="job_experiences" class="form-label">Job Experiences <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="job_experiences" name="job_experiences" rows="4"
                                  placeholder="Job experiences from latest to oldest, classified as public/non-public service with years count" required><?= esc($existing_profile['job_experiences'] ?? '') ?></textarea>
                    </div>

                    <!-- Publications -->
                    <div class="mb-3">
                        <label for="publications" class="form-label">Publications</label>
                        <textarea class="form-control" id="publications" name="publications" rows="3"
                                  placeholder="All publications by the applicant"><?= esc($existing_profile['publications'] ?? $applications[0]['publications'] ?? '') ?></textarea>
                    </div>

                    <!-- Awards -->
                    <div class="mb-3">
                        <label for="awards" class="form-label">Awards</label>
                        <textarea class="form-control" id="awards" name="awards" rows="3"
                                  placeholder="Awards like employee of the month and other workplace/citizen awards"><?= esc($existing_profile['awards'] ?? $applications[0]['awards'] ?? '') ?></textarea>
                    </div>

                    <!-- Referees -->
                    <div class="mb-3">
                        <label for="referees" class="form-label">Referees</label>
                        <textarea class="form-control" id="referees" name="referees" rows="3"
                                  placeholder="Reference contacts and information"><?= esc($existing_profile['referees'] ?? $applications[0]['referees'] ?? '') ?></textarea>
                    </div>

                    <!-- Other Information -->
                    <div class="mb-3">
                        <label for="other_information" class="form-label">Other Information <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="other_information" name="other_information" rows="3"
                                  placeholder="Other relevant information not captured in above fields" required><?= esc($existing_profile['other_information'] ?? '') ?></textarea>
                    </div>

                    <!-- Comments -->
                    <div class="mb-3">
                        <label for="comments" class="form-label">Comments</label>
                        <textarea class="form-control" id="comments" name="comments" rows="3"
                                  placeholder="Additional comments about the applicant"><?= esc($existing_profile['comments'] ?? '') ?></textarea>
                    </div>

                    <!-- Remarks -->
                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3"
                                  placeholder="Official remarks and notes" required><?= esc($existing_profile['remarks'] ?? '') ?></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="saveProfilingBtn">
                            <i class="fas fa-save me-2"></i>Save Profiling Results
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<style>
/* Fix tab text colors for better visibility */
.nav-tabs .nav-link {
    color: #333 !important;
}

.nav-tabs .nav-link.active {
    color: #000 !important;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    color: #F00F00 !important;
}

/* Sticky Profiling Form Card */
.sticky-profiling-card {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    z-index: 1000;
}
</style>

<script>
$(document).ready(function() {
    // Calculate age if date of birth is available
    const dobElement = document.getElementById('dobDisplay');
    const ageElement = document.getElementById('ageDisplay');

    if (dobElement && ageElement) {
        const dob = dobElement.textContent.trim();
        if (dob && dob !== '') {
            try {
                const birthDate = new Date(dob);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }

                if (age >= 0 && age <= 120) {
                    ageElement.textContent = age + ' years old';
                }
            } catch (e) {
                console.log('Error calculating age:', e);
            }
        }
    }

    // Handle children display safely
    try {
        const childrenData = <?= json_encode($applications[0]['children'] ?? '') ?>;
        displayChildren(childrenData);
    } catch (e) {
        console.log('Error displaying children:', e);
    }

    // Load PDFs only when Files tab is clicked to avoid loading issues
    $('#files-tab').on('click', function() {
        if (!$(this).hasClass('pdf-loaded')) {
            loadAllPDFsNative();
            $(this).addClass('pdf-loaded');
        }
    });

    // AI Profiling button functionality
    $('#aiProfilingBtn').on('click', function() {
        generateAIProfile();
    });
});

// Function to display children information
function displayChildren(childrenData) {
    const childrenDisplay = document.getElementById('childrenDisplay');
    if (!childrenDisplay) return;

    if (!childrenData || childrenData.trim() === '') {
        childrenDisplay.innerHTML = '<p class="text-muted">No children information provided.</p>';
        return;
    }

    try {
        const children = JSON.parse(childrenData);
        if (Array.isArray(children) && children.length > 0) {
            let tableHTML = `
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Name</th>
                                <th>Date of Birth</th>
                                <th>Age</th>
                                <th>Gender</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            children.forEach(child => {
                const age = child.date_of_birth ? calculateAge(child.date_of_birth) : 'N/A';
                tableHTML += `
                    <tr>
                        <td>${child.name || 'N/A'}</td>
                        <td>${child.date_of_birth || 'N/A'}</td>
                        <td>${age}</td>
                        <td>${child.gender || 'N/A'}</td>
                    </tr>
                `;
            });

            tableHTML += `
                        </tbody>
                    </table>
                </div>
            `;

            childrenDisplay.innerHTML = tableHTML;
        } else {
            childrenDisplay.innerHTML = '<p class="text-muted">No children information provided.</p>';
        }
    } catch (e) {
        // If not JSON, treat as plain text
        childrenDisplay.innerHTML = `<div class="bg-light p-3 rounded">${childrenData}</div>`;
    }
}

// Function to calculate age from date of birth
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age >= 0 ? age + ' years' : 'Invalid date';
}

// Load all PDF files with native PDF viewer
function loadAllPDFsNative() {
    const pdfViewer = document.getElementById('pdfViewer');

    if (!pdfViewer) {
        console.error('PDF viewer element not found');
        return;
    }

    // Check if already loaded
    if (pdfViewer.hasAttribute('data-loaded')) {
        return;
    }

    try {
        const files = <?= json_encode($applicant_files ?? []) ?>;

        if (!files || files.length === 0) {
            pdfViewer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> No PDF files to display.</div>';
            pdfViewer.setAttribute('data-loaded', 'true');
            return;
        }

        // Clear loading message
        pdfViewer.innerHTML = '';

        // Create iframe for each PDF file
        files.forEach((file, index) => {
            if (!file || !file.file_path) {
                return; // Skip invalid files
            }

            const fileContainer = document.createElement('div');
            fileContainer.className = 'mb-4';

            const fileHeader = document.createElement('h6');
            fileHeader.className = 'text-red border-bottom pb-2 mb-3';
            fileHeader.innerHTML = `<i class="fas fa-file-pdf me-2"></i>${file.file_title || 'PDF Document'}`;

            const iframe = document.createElement('iframe');
            iframe.src = '<?= base_url() ?>' + file.file_path;
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '1px solid #ddd';
            iframe.style.borderRadius = '4px';
            iframe.loading = 'lazy'; // Lazy load iframes

            // Add error handling for iframe
            iframe.onerror = function() {
                this.style.display = 'none';
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-warning';
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Could not load PDF file.';
                fileContainer.appendChild(errorDiv);
            };

            fileContainer.appendChild(fileHeader);
            fileContainer.appendChild(iframe);
            pdfViewer.appendChild(fileContainer);
        });

        // Mark as loaded
        pdfViewer.setAttribute('data-loaded', 'true');
    } catch (e) {
        console.error('Error loading PDFs:', e);
        pdfViewer.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error loading PDF files.</div>';
        pdfViewer.setAttribute('data-loaded', 'true');
    }
}

// AI Profiling Functions
async function generateAIProfile() {
    const btn = $('#aiProfilingBtn');
    const originalText = btn.html();

    try {
        // Show loading state
        btn.html('<i class="fas fa-spinner fa-spin"></i> Analyzing...');
        btn.prop('disabled', true);

        // Show progress modal
        Swal.fire({
            title: 'AI Profiling in Progress',
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mb-2" id="progressText">Preparing analysis...</p>
                    <p class="text-muted small">This may take a few moments</p>
                </div>
            `,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Prepare applicant data
        const applicantData = {
            <?php if (!empty($applications[0])): ?>
            first_name: <?= json_encode($applications[0]['first_name'] ?? '') ?>,
            last_name: <?= json_encode($applications[0]['last_name'] ?? '') ?>,
            gender: <?= json_encode($applications[0]['gender'] ?? '') ?>,
            date_of_birth: <?= json_encode($applications[0]['date_of_birth'] ?? '') ?>,
            place_of_origin: <?= json_encode($applications[0]['place_of_origin'] ?? '') ?>,
            citizenship: <?= json_encode($applications[0]['citizenship'] ?? '') ?>,
            email_address: <?= json_encode($applications[0]['email_address'] ?? '') ?>,
            contact_details: <?= json_encode($applications[0]['contact_details'] ?? '') ?>,
            location_address: <?= json_encode($applications[0]['location_address'] ?? '') ?>,
            marital_status: <?= json_encode($applications[0]['marital_status'] ?? '') ?>,
            date_of_marriage: <?= json_encode($applications[0]['date_of_marriage'] ?? '') ?>,
            spouse_employer: <?= json_encode($applications[0]['spouse_employer'] ?? '') ?>,
            id_numbers: <?= json_encode($applications[0]['id_numbers'] ?? '') ?>,
            offence_convicted: <?= json_encode($applications[0]['offence_convicted'] ?? '') ?>,
            current_employer: <?= json_encode($applications[0]['current_employer'] ?? '') ?>,
            current_position: <?= json_encode($applications[0]['current_position'] ?? '') ?>,
            current_salary: <?= json_encode($applications[0]['current_salary'] ?? '') ?>,
            is_public_servant: <?= json_encode($applications[0]['is_public_servant'] ?? false) ?>,
            public_service_file_number: <?= json_encode($applications[0]['public_service_file_number'] ?? '') ?>,
            employee_of_org_id: <?= json_encode($applications[0]['employee_of_org_id'] ?? '') ?>,
            children: <?= json_encode($applications[0]['children'] ?? '') ?>,
            referees: <?= json_encode($applications[0]['referees'] ?? '') ?>,
            publications: <?= json_encode($applications[0]['publications'] ?? '') ?>,
            awards: <?= json_encode($applications[0]['awards'] ?? '') ?>,
            how_did_you_hear_about_us: <?= json_encode($applications[0]['how_did_you_hear_about_us'] ?? '') ?>
            <?php endif; ?>
        };

        // Get files data and exercise closing date
        const applicantFiles = <?= json_encode($applicant_files ?? []) ?>;
        const exerciseClosingDate = '<?= $exercise['publish_date_to'] ?? '' ?>';

        // Process PDF files and convert to images for analysis
        const processedFiles = [];
        if (applicantFiles && applicantFiles.length > 0) {
            updateProgressText('Reading and analyzing documents...');

            for (let i = 0; i < applicantFiles.length; i++) {
                const file = applicantFiles[i];
                updateProgressText(`Converting ${file.file_title || 'document'} to images (${i + 1}/${applicantFiles.length})...`);

                try {
                    const filePath = '<?= base_url() ?>' + file.file_path;
                    const fileName = file.file_title || file.original_filename || 'document';

                    // Load PDF and convert to images
                    const pdf = await loadPDFFromURL(filePath);
                    const totalPages = pdf.numPages;

                    updateProgressText(`Thoroughly scanning ${fileName} (${totalPages} pages) - extracting ALL education qualifications, performance reports, and recruitment data...`);

                    // Convert all pages to images
                    const imageData = await convertPDFPagesToImages(pdf, 1, totalPages);

                    if (imageData && imageData.length > 0) {
                        processedFiles.push({
                            file_name: fileName,
                            file_title: file.file_title,
                            file_description: file.file_description,
                            total_pages: totalPages,
                            images: imageData
                        });
                    }
                } catch (fileError) {
                    console.warn(`Failed to process file ${file.file_title}:`, fileError);
                    // Continue with other files even if one fails
                }
            }
        }

        updateProgressText('Performing comprehensive analysis - extracting education, work experience, training, skills, performance reports, references, and awards with complete details...');

        // Send to AI for analysis with both data and file content
        const response = await fetch('<?= base_url('api/prescreening/generate-profiling') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                applicant_data: applicantData,
                applicant_files: applicantFiles, // metadata
                processed_files: processedFiles, // actual PDF content
                exercise_closing_date: exerciseClosingDate // for age calculation
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'AI profiling failed');
        }

        // Close progress modal
        Swal.close();

        // Populate the form with AI results
        populateProfilingForm(data.profiling);

        // Show success message
        Swal.fire({
            title: 'AI Profiling Complete!',
            text: 'The profiling form has been automatically filled with comprehensive details including: complete education qualifications, detailed work experiences, training history, skills assessment, performance reports, professional references, awards, and all recruitment-critical information. All data has been cross-referenced and verified. Please review and edit as needed.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#F00F00'
        });

    } catch (error) {
        console.error('AI profiling error:', error);

        // Close any open modals
        Swal.close();

        // Show error message
        Swal.fire({
            title: 'AI Profiling Failed',
            text: error.message || 'An error occurred during AI profiling. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
            confirmButtonColor: '#F00F00'
        });
    } finally {
        // Reset button
        btn.html(originalText);
        btn.prop('disabled', false);
    }
}

// Function to populate the profiling form with AI results
function populateProfilingForm(profilingData) {
    if (!profilingData || !profilingData.profiling_data) {
        console.error('Invalid profiling data structure');
        return;
    }

    const data = profilingData.profiling_data;

    // Populate form fields
    if (data.full_name) $('#full_name').val(data.full_name);
    if (data.sex) $('#sex').val(data.sex);
    if (data.bdate_age) $('#bdate_age').val(data.bdate_age);
    if (data.place_origin) $('#place_origin').val(data.place_origin);
    if (data.contact_details) $('#contact_details').val(data.contact_details);
    if (data.id_document_numbers) $('#id_document_numbers').val(data.id_document_numbers);
    if (data.current_employer) $('#current_employer').val(data.current_employer);
    if (data.current_position) $('#current_position').val(data.current_position);
    if (data.address_location) $('#address_location').val(data.address_location);
    if (data.qualification_text) $('#qualification_text').val(data.qualification_text);
    if (data.other_trainings) $('#other_trainings').val(data.other_trainings);
    if (data.knowledge) $('#knowledge').val(data.knowledge);
    if (data.skills_competencies) $('#skills_competencies').val(data.skills_competencies);
    if (data.job_experiences) $('#job_experiences').val(data.job_experiences);
    if (data.publications) $('#publications').val(data.publications);
    if (data.awards) $('#awards').val(data.awards);
    if (data.referees) $('#referees').val(data.referees);
    if (data.other_information) $('#other_information').val(data.other_information);
    if (data.comments) $('#comments').val(data.comments);
    if (data.remarks) $('#remarks').val(data.remarks);

    // Auto-resize textareas to fit content
    $('textarea').each(function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Add visual feedback
    $('.form-control').addClass('ai-populated');
    setTimeout(() => {
        $('.form-control').removeClass('ai-populated');
    }, 3000);
}

// Helper function to update progress text
function updateProgressText(text) {
    const progressElement = document.getElementById('progressText');
    if (progressElement) {
        progressElement.textContent = text;
    }
}

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// Load PDF from URL
async function loadPDFFromURL(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        return pdf;
    } catch (error) {
        console.error('Error loading PDF:', error);
        throw error;
    }
}

// Convert PDF pages to images for multimodal AI analysis
async function convertPDFPagesToImages(pdf, startPage, endPage) {
    const images = [];

    // Use moderate scale for better quality without huge file sizes
    const scale = 1.5;

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        try {
            const page = await pdf.getPage(pageNum);
            const viewport = page.getViewport({ scale: scale });

            // Create canvas
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render page to canvas
            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise;

            // Convert to base64 image
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            const base64Data = imageData.split(',')[1];

            // Calculate approximate size
            const sizeKB = Math.round((base64Data.length * 3) / 4 / 1024);

            images.push({
                page_number: pageNum,
                image_data: base64Data,
                size_kb: sizeKB,
                width: canvas.width,
                height: canvas.height
            });

        } catch (error) {
            console.warn(`Failed to convert page ${pageNum}:`, error);
            // Continue with other pages
        }
    }

    return images;
}
</script>

<style>
/* AI populated field animation */
.ai-populated {
    background-color: #e8f5e8 !important;
    border-color: #28a745 !important;
    transition: all 0.3s ease;
}
</style>
<?= $this->endSection() ?>

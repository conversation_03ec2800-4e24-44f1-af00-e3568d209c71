<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('dashboard') ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profiled_applicants') ?>">Profiled Applicants</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($exercise['exercise_name']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Profiled Applicants</h2>
                    <p class="text-muted mb-0"><?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('profiled_applicants') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-info mb-2">
                                <i class="fas fa-clipboard-list me-2"></i>Exercise Details
                            </h6>
                            <p class="mb-1"><strong>Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                            <p class="mb-0"><strong>Status:</strong> 
                                <span class="badge bg-success"><?= esc(ucfirst($exercise['status'])) ?></span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info mb-2">
                                <i class="fas fa-chart-bar me-2"></i>Statistics
                            </h6>
                            <p class="mb-0"><strong>Total Profiled Applicants:</strong> 
                                <span class="badge bg-primary"><?= count($applicants) ?></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profiled Applicants List -->
    <div class="row">
        <div class="col-12">
            <div class="card hover-card">
                <div class="card-header bg-red text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-check"></i> Profiled Applicants (<?= count($applicants) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applicants)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            No profiled applicants found for this exercise.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table id="applicantsTable" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="30%">Full Name</th>
                                        <th width="15%">Gender</th>
                                        <th width="20%">Contact Details</th>
                                        <th width="15%">Profile Count</th>
                                        <th width="15%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $count = 1; ?>
                                    <?php foreach ($applicants as $applicant): ?>
                                        <tr>
                                            <td><?= $count++ ?></td>
                                            <td>
                                                <strong><?= esc($applicant['full_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $applicant['gender'] === 'Male' ? 'primary' : 'info' ?>">
                                                    <?= esc($applicant['gender']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($applicant['contact_details']) ?></td>
                                            <td>
                                                <span class="badge bg-success"><?= esc($applicant['profile_count']) ?></span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('profiled_applicants/applicant/' . $applicant['applicant_id'] . '/exercise/' . $exercise['id']) ?>"
                                                   class="btn btn-sm btn-outline-primary" title="View Profile Details">
                                                    <i class="fas fa-eye"></i> View Profile
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Information
                    </h6>
                    <p class="card-text">
                        This list shows all applicants who have been profiled for the exercise "<?= esc($exercise['exercise_name']) ?>". 
                        Click "View Profile" to see the detailed profiling information for each applicant.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicantsTable').DataTable({
        responsive: true,
        order: [[1, 'asc']], // Sort by name
        language: {
            search: "Search applicants:",
            lengthMenu: "Show _MENU_ applicants per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applicants",
            emptyTable: "No profiled applicants available",
        }
    });
});
</script>
<?= $this->endSection() ?>

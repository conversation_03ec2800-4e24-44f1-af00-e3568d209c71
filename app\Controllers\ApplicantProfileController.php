<?php

namespace App\Controllers;

class ApplicantProfileController extends BaseController
{
    protected $session;
    protected $applicantsModel;
    protected $experiencesModel;
    protected $applicantEducationModel;
    protected $educationModel;
    protected $applicantFilesModel;
    protected $exerciseModel;
    protected $preScreenModel;
    protected $applicationModel;
    protected $applicationFilesModel;
    protected $profileModel;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->applicantsModel = new \App\Models\ApplicantsModel();
        $this->experiencesModel = new \App\Models\ApplicantsExperiencesModel();
        $this->applicantEducationModel = new \App\Models\ApplicantEducationModel();
        $this->educationModel = new \App\Models\EducationLevelsModel();
        $this->applicantFilesModel = new \App\Models\ApplicantFilesModel();
        $this->exerciseModel = new \App\Models\ExerciseModel();
        $this->preScreenModel = new \App\Models\ExercisePositionsPreScreenModel();
        $this->applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $this->applicationFilesModel = new \App\Models\AppxApplicationFilesModel();
        $this->profileModel = new \App\Models\AppxApplicationProfileModel();
    }

    /**
     * Display list of exercises for profiling (GET)
     */
    public function index()
    {
        // Get organization ID from session
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercises with status = 'selection' for this organization only
        $exercises = $this->exerciseModel->where('status', 'selection')
                                        ->where('org_id', $orgId)
                                        ->findAll();

        $data = [
            'title' => 'Exercises Available for Profiling',
            'menu' => 'profiling',
            'exercises' => $exercises
        ];

        return view('application_profiling/application_profiling_exercise_list', $data);
    }

    /**
     * Display applicants for profiling in a specific exercise (GET)
     */
    public function exerciseApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercise details
        $exercise = $this->exerciseModel->where('id', $exerciseId)
                                      ->where('org_id', $orgId)
                                      ->first();

        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('profile_applications_exercise'));
        }

        // Get positions marked for pre-screening
        $preScreeningPositions = $this->preScreenModel->getByExerciseId($exerciseId);
        $preScreeningPositionIds = array_column($preScreeningPositions, 'position_id');

        // Get applicants for profiling using model method
        $applicants = $this->applicationModel->getApplicantsForProfiling($exerciseId, $orgId, $preScreeningPositionIds);

        $data = [
            'title' => 'Applicants for Profiling in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'profiling',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('application_profiling/application_profiling_exercise_applicants', $data);
    }

    /**
     * Display applicant profile for profiling (GET)
     */
    public function applicantProfile($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercise details
        $exercise = $this->exerciseModel->where('id', $exerciseId)
                                      ->where('org_id', $orgId)
                                      ->first();

        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('profile_applications_exercise'));
        }

        // Get all applications for this applicant in this exercise
        $applications = $this->applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('profile_applications_exercise/exercise/' . $exerciseId . '/applicants'));
        }

        // Get applicant name from first application
        $applicantName = $applications[0]['full_name'];

        // Get all files for this applicant across all applications in this exercise
        $applicantFiles = [];
        $uniqueFiles = [];

        foreach ($applications as $application) {
            $files = $this->applicationFilesModel->getFilesByApplicationId($application['id']);
            foreach ($files as $file) {
                // Create a unique identifier for the file
                $uniqueKey = '';

                if (!empty($file['applicant_file_id'])) {
                    $uniqueKey = 'file_id_' . $file['applicant_file_id'];
                } elseif (!empty($file['file_path'])) {
                    $uniqueKey = 'file_path_' . $file['file_path'];
                } else {
                    $uniqueKey = 'file_title_' . $file['applicant_id'] . '_' . $file['file_title'];
                }

                // Only add if we haven't seen this unique file before
                if (!isset($uniqueFiles[$uniqueKey])) {
                    $uniqueFiles[$uniqueKey] = true;
                    $applicantFiles[] = $file;
                }
            }
        }

        // Get existing profile data for this applicant and exercise
        $existingProfile = $this->profileModel->where('applicant_id', $applicantId)
                                            ->where('exercise_id', $exerciseId)
                                            ->first();

        $data = [
            'title' => 'Profiling Profile: ' . esc($applicantName),
            'menu' => 'profiling',
            'exercise' => $exercise,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications,
            'applicant_files' => $applicantFiles,
            'existing_profile' => $existingProfile
        ];

        return view('application_profiling/application_profiling_applicant_profile', $data);
    }

    /**
     * Save profiling results (POST)
     */
    public function saveProfilingResults()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get form data
        $applicantId = $this->request->getPost('applicant_id');
        $exerciseId = $this->request->getPost('exercise_id');

        // Prepare data for saving
        $data = [
            'applicant_id' => $applicantId,
            'exercise_id' => $exerciseId,
            'full_name' => $this->request->getPost('full_name'),
            'sex' => $this->request->getPost('sex'),
            'bdate_age' => $this->request->getPost('bdate_age'),
            'place_origin' => $this->request->getPost('place_origin'),
            'contact_details' => $this->request->getPost('contact_details'),
            'id_document_numbers' => $this->request->getPost('id_document_numbers'),
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'address_location' => $this->request->getPost('address_location'),
            'qualification_text' => $this->request->getPost('qualification_text'),
            'other_trainings' => $this->request->getPost('other_trainings'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'other_information' => $this->request->getPost('other_information'),
            'comments' => $this->request->getPost('comments'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        // Start database transaction
        $db = \Config\Database::connect();
        $db->transStart();

        // Check if profile already exists
        $existingProfile = $this->profileModel->where('applicant_id', $applicantId)
                                            ->where('exercise_id', $exerciseId)
                                            ->first();

        $profileSaved = false;
        if ($existingProfile) {
            // Update existing profile
            $profileSaved = $this->profileModel->update($existingProfile['id'], $data);
        } else {
            // Create new profile
            $profileSaved = $this->profileModel->insert($data);
        }

        // Update all applications for this applicant in this exercise
        $applicationUpdateData = [
            'profile_status' => 'profiled',
            'profile_details' => json_encode([
                'profiled_by' => $this->session->get('user_id'),
                'profiled_at' => date('Y-m-d H:i:s'),
                'profile_summary' => 'Comprehensive AI-assisted profiling completed'
            ]),
            'profiled_by' => $this->session->get('user_id'),
            'profiled_at' => date('Y-m-d H:i:s')
        ];

        $applicationsUpdated = $this->applicationModel->where('applicant_id', $applicantId)
                                                     ->where('exercise_id', $exerciseId)
                                                     ->set($applicationUpdateData)
                                                     ->update();

        // Complete transaction
        $db->transComplete();

        if ($db->transStatus() === false || !$profileSaved || !$applicationsUpdated) {
            $this->session->setFlashdata('error', 'Failed to save profiling results.');
        } else {
            $this->session->setFlashdata('success', 'Profiling results saved successfully and all applications updated.');
        }

        return redirect()->to(base_url('profile_applications_exercise/applicant_profile/' . $applicantId . '/' . $exerciseId));
    }

    /**
     * Display positions for a specific exercise (GET)
     */
    public function exercisePositions($exerciseId)
    {
        // Mock exercise data based on ExerciseModel structure
        $exercise = [
            'id' => $exerciseId,
            'org_id' => 1,
            'exercise_name' => $exerciseId == 1 ? 'IT Recruitment Exercise 2024' : ($exerciseId == 2 ? 'Finance Department Recruitment 2024' : 'Administrative Services Recruitment 2024'),
            'gazzetted_no' => 'GAZ-2024-00' . $exerciseId,
            'gazzetted_date' => '2024-01-01',
            'advertisement_no' => 'ADV-2024-00' . $exerciseId,
            'advertisement_date' => '2024-01-05',
            'mode_of_advertisement' => 'Online and Print Media',
            'publish_date_from' => '2024-01-10',
            'publish_date_to' => '2024-02-10',
            'description' => 'Annual recruitment exercise for various positions',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock positions data with position group information
        $positions = [
            [
                'id' => 1,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Developer',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 12',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 8,
                'pending_prescreen_count' => 3,
                'passed_prescreen_count' => 5,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 2,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 11',
                'award' => 'K55,000 - K65,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 6,
                'pending_prescreen_count' => 2,
                'passed_prescreen_count' => 4,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 3,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-003',
                'designation' => 'Network Administrator',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 10',
                'award' => 'K45,000 - K55,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 5,
                'pending_prescreen_count' => 1,
                'passed_prescreen_count' => 4,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 4,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-004',
                'designation' => 'Systems Analyst',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 11',
                'award' => 'K55,000 - K65,000',
                'location' => 'Lae, Morobe',
                'applications_count' => 4,
                'pending_prescreen_count' => 1,
                'passed_prescreen_count' => 3,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 5,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-005',
                'designation' => 'IT Support Specialist',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 9',
                'award' => 'K35,000 - K45,000',
                'location' => 'Mount Hagen, WHP',
                'applications_count' => 2,
                'pending_prescreen_count' => 0,
                'passed_prescreen_count' => 2,
                'failed_prescreen_count' => 0
            ]
        ];

        $data = [
            'title' => 'Positions for Profiling',
            'menu' => 'profiling',
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('application_profiling/application_profiling_exercise_positions', $data);
    }

    /**
     * Display position profile with applicant profiles in table format (GET)
     */
    public function positionProfile($positionId)
    {
        // Mock position data
        $position = [
            'id' => $positionId,
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'position_reference' => 'IT-001',
            'designation' => 'Senior Software Developer',
            'position_group_name' => 'Information Technology',
            'classification' => 'Grade 12',
            'award' => 'K65,000 - K75,000',
            'location' => 'Port Moresby, NCD',
            'qualifications' => 'Bachelor degree in Computer Science or related field',
            'knowledge' => 'Software development, database management, system analysis',
            'skills_competencies' => 'Programming languages, project management, team leadership',
            'job_experiences' => 'Minimum 5 years software development experience'
        ];

        // Mock applicant profiles data based on AppxApplicationProfileModel structure
        $applicantProfiles = [
            [
                'id' => 1,
                'application_id' => 1,
                'name' => 'John Michael Doe',
                'sex' => 'Male',
                'age' => 34,
                'place_origin' => 'Port Moresby, NCD',
                'contact_details' => '+675 123 4567, <EMAIL>',
                'nid_number' => 'NID123456789',
                'current_employer' => 'TechSoft PNG Ltd',
                'current_position' => 'Software Developer',
                'address_location' => '123 Main Street, Port Moresby',
                'qualification_text' => 'Bachelor of Computer Science, University of PNG (2012)',
                'other_trainings' => 'Certified Java Developer (2018), AWS Cloud Practitioner (2020)',
                'knowledge' => 'Java, Python, JavaScript, Database Design, Cloud Computing',
                'skills_competencies' => 'Full-stack development, Team leadership, Agile methodologies',
                'job_experiences' => 'Software Developer at TechSoft PNG (2015-Present), Junior Developer at DataSys (2013-2015)',
                'publications' => 'Co-authored "Modern Web Development in PNG" (2021)',
                'awards' => 'Best Developer Award - TechSoft PNG (2020)',
                'referees' => 'Mr. Peter Smith, CTO, TechSoft PNG, +************',
                'comments' => 'Excellent technical skills with strong leadership potential',
                'remarks' => 'Highly recommended for senior position'
            ],
            [
                'id' => 2,
                'application_id' => 2,
                'name' => 'Sarah Jane Wilson',
                'sex' => 'Female',
                'age' => 29,
                'place_origin' => 'Lae, Morobe',
                'contact_details' => '+************, <EMAIL>',
                'nid_number' => 'NID987654321',
                'current_employer' => 'Digital Solutions PNG',
                'current_position' => 'Senior Programmer',
                'address_location' => '456 Oak Avenue, Lae',
                'qualification_text' => 'Bachelor of Information Technology, PNG University of Technology (2016)',
                'other_trainings' => 'Microsoft Azure Certification (2019), Scrum Master Certification (2021)',
                'knowledge' => 'C#, .NET Framework, SQL Server, Azure Cloud Services',
                'skills_competencies' => 'Software architecture, Database optimization, Project management',
                'job_experiences' => 'Senior Programmer at Digital Solutions PNG (2018-Present), Programmer at CodeCraft (2016-2018)',
                'publications' => '',
                'awards' => 'Innovation Award - Digital Solutions PNG (2021)',
                'referees' => 'Ms. Mary Johnson, Manager, Digital Solutions PNG, +************',
                'comments' => 'Strong technical background with excellent problem-solving skills',
                'remarks' => 'Suitable for senior technical role'
            ],
            [
                'id' => 3,
                'application_id' => 3,
                'name' => 'Robert James Kila',
                'sex' => 'Male',
                'age' => 31,
                'place_origin' => 'Mount Hagen, WHP',
                'contact_details' => '+************, <EMAIL>',
                'nid_number' => 'NID456789123',
                'current_employer' => 'Government IT Department',
                'current_position' => 'Systems Analyst',
                'address_location' => '789 Pine Road, Mount Hagen',
                'qualification_text' => 'Bachelor of Computer Science, Divine Word University (2014)',
                'other_trainings' => 'CISSP Certification (2020), ITIL Foundation (2019)',
                'knowledge' => 'System analysis, Network security, Database administration',
                'skills_competencies' => 'Systems design, Security implementation, Documentation',
                'job_experiences' => 'Systems Analyst at Government IT (2017-Present), IT Support at TechServ (2015-2017)',
                'publications' => '',
                'awards' => 'Outstanding Service Award - Government IT (2022)',
                'referees' => 'Mr. David Brown, Director, Government IT, +675 555 6666',
                'comments' => 'Solid government experience with security focus',
                'remarks' => 'Good candidate with relevant government experience'
            ]
        ];

        // Mock scoring data
        $scoringData = [
            'criteria' => [
                'Age' => ['scores' => [5, 7, 7], 'out_of' => 8],
                'Qualification' => ['scores' => ['', '', ''], 'out_of' => 0],
                'Experience' => ['scores' => [0, 0, 0], 'out_of' => 0],
                'Trainings' => ['scores' => ['', '', ''], 'out_of' => 0],
                'Skills/Competencies' => ['scores' => ['', '', ''], 'out_of' => 0],
                'Knowledge' => ['scores' => ['', '', ''], 'out_of' => 0],
                'Capability' => ['scores' => ['', '', ''], 'out_of' => 0],
                'Public Service Status' => ['scores' => ['', '', ''], 'out_of' => 0]
            ],
            'totals' => ['', '', '', 0]
        ];

        // Mock short list and eliminated data
        $shortList = [
            'Nelson Boli ()',
            'Meleny Pius ()',
            'Maris Yano ()',
            'Nelson Albert ()',
            'Tom Kisambo ()',
            'Paul Tohan Opi ()',
            'Joe Tep Kembo ()',
            'Steven Sipi Kongai ()',
            'Meleny Pius ()',
            'Timon Philip ()',
            'Vincent Kazuk Sonk ()',
            'Norman Kokele ()',
            'Sakare Saki Sanangke ()',
            'Samuel Gaso ()',
            'Patrick Tilnaek ()',
            'Issac Pep ()',
            'Michael Onin ()'
        ];

        $eliminated = [
            'Grace Wepe Geoffrey - Eliminate'
        ];

        $data = [
            'title' => 'Position Profile: ' . $position['designation'],
            'menu' => 'profiling',
            'position' => $position,
            'applicant_profiles' => $applicantProfiles,
            'scoring_data' => $scoringData,
            'short_list' => $shortList,
            'eliminated' => $eliminated
        ];

        return view('application_profiling/application_profiling_position_profile', $data);
    }



    public function profile()
    {
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Get work experiences
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                            ->orderBy('date_from', 'DESC')
                                            ->findAll();

        // Get applicant's education records using automatic model features
        $education = $this->applicantEducationModel->where('applicant_id', $applicant_id)
                                                 ->orderBy('date_from', 'DESC')
                                                 ->findAll();

        // Get education levels from adx_education table for dropdowns
        $education_data = $this->educationModel->findAll();

        // Get applicant's files
        $files = $this->applicantFilesModel->where('applicant_id', $applicant_id)
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        $data = [
            'title' => 'Edit Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_data' => $education_data,
            'files' => $files,
            'education_levels' => [
                1 => 'Elementary',
                2 => 'High School',
                3 => 'Vocational',
                4 => 'College',
                5 => 'Post Graduate'
            ]
        ];

        return view('applicant/applicant_profile', $data);
    }

    // Note: All AJAX-based profile update methods have been removed.
    // Profile updates now use standard CodeIgniter 4 CRUD operations via ApplicantController.
    // Only the profiling-related methods for application management remain in this controller.
}
